<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Control de Acceso - Portón</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      body {
        font-family: Arial, sans-serif;
        background: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background: #2c3e50;
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }
      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #3498db;
      }
      .tabs {
        display: flex;
        margin-bottom: 20px;
      }
      .tab {
        padding: 10px 20px;
        background: #ecf0f1;
        border: none;
        cursor: pointer;
        border-radius: 4px 4px 0 0;
        margin-right: 5px;
      }
      .tab.active {
        background: white;
        border-bottom: 2px solid #3498db;
      }
      .tab-content {
        background: white;
        padding: 20px;
        border-radius: 0 8px 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .form-group {
        margin-bottom: 15px;
      }
      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      .form-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .btn-primary {
        background: #3498db;
        color: white;
      }
      .btn-danger {
        background: #e74c3c;
        color: white;
      }
      .btn:hover {
        opacity: 0.8;
      }
      .table {
        width: 100%;
        border-collapse: collapse;
      }
      .table th,
      .table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
      }
      .table th {
        background: #f8f9fa;
        font-weight: bold;
      }
      .status-authorized {
        color: #27ae60;
        font-weight: bold;
      }
      .status-denied {
        color: #e74c3c;
        font-weight: bold;
      }
      .location-entrada {
        background: #e8f5e8;
        padding: 4px 8px;
        border-radius: 4px;
      }
      .location-salida {
        background: #fff3cd;
        padding: 4px 8px;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🏠 Control de Acceso - Portón Fraccionamiento</h1>
        <p>Sistema de monitoreo y control en tiempo real</p>
      </div>

      <div class="stats" id="stats">
        <!-- Estadísticas se cargan aquí -->
      </div>

      <div class="tabs">
        <button class="tab active" onclick="showTab('logs')">
          📊 Registros
        </button>
        <button class="tab" onclick="showTab('plates')">
          🚗 Placas Autorizadas
        </button>
        <button class="tab" onclick="showTab('control')">
          🎛️ Control Manual
        </button>
      </div>

      <div id="logs-tab" class="tab-content">
        <h3>Registros Recientes</h3>
        <div style="margin-bottom: 15px">
          <select id="hours-filter" onchange="loadLogs()">
            <option value="1">Última hora</option>
            <option value="6">Últimas 6 horas</option>
            <option value="24" selected>Últimas 24 horas</option>
            <option value="168">Última semana</option>
          </select>
        </div>
        <div id="logs-container">
          <!-- Logs se cargan aquí -->
        </div>
      </div>

      <div id="plates-tab" class="tab-content" style="display: none">
        <h3>Gestión de Placas Autorizadas</h3>

        <div
          style="
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
          "
        >
          <h4>Agregar Nueva Placa</h4>
          <div style="display: flex; gap: 10px; align-items: end">
            <div class="form-group" style="flex: 1">
              <label>Placa:</label>
              <input
                type="text"
                id="new-plate"
                placeholder="ABC1234"
                style="text-transform: uppercase"
              />
            </div>
            <div class="form-group" style="flex: 2">
              <label>Nombre del Propietario:</label>
              <input type="text" id="new-name" placeholder="Juan Pérez" />
            </div>
            <button class="btn btn-primary" onclick="addPlate()">
              Agregar
            </button>
          </div>
        </div>

        <div id="plates-container">
          <!-- Placas se cargan aquí -->
        </div>
      </div>

      <div id="control-tab" class="tab-content" style="display: none">
        <h3>Control Manual del Portón</h3>

        <div
          style="
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
          "
        >
          <h4>⚠️ Apertura Manual</h4>
          <p>Use esta función solo en casos de emergencia o mantenimiento</p>

          <div style="margin: 20px 0">
            <label for="duration">Duración (segundos):</label>
            <select id="duration" style="margin: 0 10px; padding: 5px">
              <option value="5">5 segundos</option>
              <option value="10" selected>10 segundos</option>
              <option value="15">15 segundos</option>
              <option value="20">20 segundos</option>
              <option value="30">30 segundos</option>
            </select>
          </div>

          <button
            class="btn btn-primary"
            onclick="openGateManually()"
            style="font-size: 16px; padding: 15px 30px"
          >
            🚪 Abrir Portón
          </button>
        </div>

        <div
          style="
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
          "
        >
          <strong>⚠️ Advertencia:</strong> El portón se abrirá inmediatamente al
          presionar el botón. Asegúrese de que el área esté despejada.
        </div>
      </div>
    </div>

    <script>
      // Cargar datos al iniciar
      loadStats();
      loadLogs();
      loadPlates();

      // Actualizar cada 30 segundos
      setInterval(() => {
        loadStats();
        loadLogs();
      }, 30000);

      function showTab(tabName) {
        // Ocultar todas las pestañas
        document
          .querySelectorAll(".tab-content")
          .forEach((tab) => (tab.style.display = "none"));
        document
          .querySelectorAll(".tab")
          .forEach((tab) => tab.classList.remove("active"));

        // Mostrar pestaña seleccionada
        document.getElementById(tabName + "-tab").style.display = "block";
        event.target.classList.add("active");

        if (tabName === "plates") loadPlates();
      }

      async function loadStats() {
        try {
          const response = await fetch("/api/stats");
          const stats = await response.json();

          document.getElementById("stats").innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${stats.total_accesos}</div>
                        <div>Total Accesos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.autorizados}</div>
                        <div>Autorizados</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.no_autorizados}</div>
                        <div>No Autorizados</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.entradas}</div>
                        <div>Entradas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.salidas}</div>
                        <div>Salidas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.total_placas}</div>
                        <div>Placas Registradas</div>
                    </div>
                `;
        } catch (error) {
          console.error("Error cargando estadísticas:", error);
        }
      }

      async function loadLogs() {
        try {
          const hours = document.getElementById("hours-filter").value;
          const response = await fetch(`/api/logs?hours=${hours}`);
          const logs = await response.json();

          let html =
            '<table class="table"><thead><tr><th>Fecha/Hora</th><th>Ubicación</th><th>Placa</th><th>Estado</th><th>Imagen</th></tr></thead><tbody>';

          logs.forEach((log) => {
            const date = new Date(log.timestamp).toLocaleString("es-ES");
            const location = log.ubicacion || "N/A";
            const plate = log.placa || "Sin placa";
            const status = log.autorizado ? "Autorizado" : "Denegado";
            const statusClass = log.autorizado
              ? "status-authorized"
              : "status-denied";
            const locationClass =
              location === "entrada" ? "location-entrada" : "location-salida";

            // Construir enlace de imagen usando el nuevo endpoint
            const imageLink = log.imagen
              ? `<a href="/images/${log.imagen}" target="_blank">Ver imagen</a>`
              : "Sin imagen";

            html += `
                <tr>
                    <td>${date}</td>
                    <td><span class="${locationClass}">${location.toUpperCase()}</span></td>
                    <td>${plate}</td>
                    <td><span class="${statusClass}">${status}</span></td>
                    <td>${imageLink}</td>
                </tr>
            `;
          });

          html += "</tbody></table>";
          document.getElementById("logs-container").innerHTML = html;
        } catch (error) {
          console.error("Error cargando logs:", error);
        }
      }

      async function loadPlates() {
        try {
          const response = await fetch("/api/plates");
          const plates = await response.json();

          let html =
            '<table class="table"><thead><tr><th>Placa</th><th>Propietario</th><th>Acciones</th></tr></thead><tbody>';

          Object.entries(plates).forEach(([plate, info]) => {
            html += `
                        <tr>
                            <td><strong>${plate}</strong></td>
                            <td>${info.name || "Sin nombre"}</td>
                            <td><button class="btn btn-danger" onclick="deletePlate('${plate}')">Eliminar</button></td>
                        </tr>
                    `;
          });

          html += "</tbody></table>";
          document.getElementById("plates-container").innerHTML = html;
        } catch (error) {
          console.error("Error cargando placas:", error);
        }
      }

      async function addPlate() {
        const plate = document
          .getElementById("new-plate")
          .value.trim()
          .toUpperCase();
        const name = document.getElementById("new-name").value.trim();

        if (!plate) {
          alert("Por favor ingresa una placa");
          return;
        }

        try {
          const response = await fetch("/api/plates", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ plate, name }),
          });

          if (response.ok) {
            document.getElementById("new-plate").value = "";
            document.getElementById("new-name").value = "";
            loadPlates();
            loadStats();
            alert("Placa agregada exitosamente");
          } else {
            alert("Error agregando placa");
          }
        } catch (error) {
          console.error("Error:", error);
          alert("Error de conexión");
        }
      }

      async function deletePlate(plate) {
        if (!confirm(`¿Estás seguro de eliminar la placa ${plate}?`)) return;

        try {
          const response = await fetch(`/api/plates/${plate}`, {
            method: "DELETE",
          });

          if (response.ok) {
            loadPlates();
            loadStats();
            alert("Placa eliminada exitosamente");
          } else {
            alert("Error eliminando placa");
          }
        } catch (error) {
          console.error("Error:", error);
          alert("Error de conexión");
        }
      }

      async function openGateManually() {
        const duration = parseInt(document.getElementById("duration").value);

        if (
          !confirm(`¿Está seguro de abrir el portón por ${duration} segundos?`)
        ) {
          return;
        }

        try {
          const response = await fetch("/api/open-gate", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ duration }),
          });

          const result = await response.json();

          if (response.ok) {
            alert(`✅ ${result.message}`);
            loadLogs(); // Actualizar logs
          } else {
            alert(`❌ Error: ${result.error}`);
          }
        } catch (error) {
          console.error("Error:", error);
          alert("❌ Error de conexión");
        }
      }
    </script>
  </body>
</html>
