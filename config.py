import os
from dataclasses import dataclass

@dataclass
class Config:
    API_KEY: str = "dd975ff0f042e189131a14f8a32cfe5bb1bd66ad"
    RTSP_CAMERA_ENTRADA: str = "rtsp://admin:admin@192.168.100.44:554/11"
    RTSP_CAMERA_SALIDA: str = "rtsp://admin:admin@192.168.100.45:554/11"
    SNAPSHOTS_DIR: str = "snapshots"
    LOGS_DIR: str = "logs"
    RELAY_PIN: int = 17
    CYCLE_INTERVAL: int = 5
    AUTHORIZED_PLATES_FILE: str = "authorized_license_plates.json"
    
    # Pines para sensores JSN-SR04T
    PIN_SENSOR_ENTRADA_TRIGGER: int = 23
    PIN_SENSOR_ENTRADA_ECHO: int = 24
    PIN_SENSOR_SALIDA_TRIGGER: int = 25
    PIN_SENSOR_SALIDA_ECHO: int = 8
    PIN_SENSOR_PASADA_TRIGGER: int = 7
    PIN_SENSOR_PASADA_ECHO: int = 1

config = Config()
