from flask import Flask, render_template, jsonify, request, send_file
import json
import os
from datetime import datetime, timedelta
from config import config

# Importar la función para obtener el relé compartido
from gate_control_system import get_gate_relay

app = Flask(__name__)

class WebController:
    def __init__(self):
        self.authorized_plates = self.load_authorized_plates()
        # Usar instancia compartida del relé
        self.gate_relay = get_gate_relay()
    
    def load_authorized_plates(self):
        try:
            with open(config.AUTHORIZED_PLATES_FILE, "r") as f:
                return json.load(f)
        except:
            return {}
    
    def save_authorized_plates(self):
        with open(config.AUTHORIZED_PLATES_FILE, "w") as f:
            json.dump(self.authorized_plates, f, indent=2)
    
    def get_recent_logs(self, hours=24):
        logs = []
        log_dir = config.LOGS_DIR
        
        if not os.path.exists(log_dir):
            return logs
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        for filename in os.listdir(log_dir):
            if filename.startswith("event_") and filename.endswith(".json"):
                filepath = os.path.join(log_dir, filename)
                try:
                    with open(filepath, "r") as f:
                        log_data = json.load(f)
                        log_time = datetime.fromisoformat(log_data["timestamp"])
                        if log_time > cutoff_time:
                            logs.append(log_data)
                except:
                    continue
        
        return sorted(logs, key=lambda x: x["timestamp"], reverse=True)

controller = WebController()

@app.route('/')
def dashboard():
    return render_template('dashboard.html')

@app.route('/api/logs')
def get_logs():
    hours = request.args.get('hours', 24, type=int)
    logs = controller.get_recent_logs(hours)
    return jsonify(logs)

@app.route('/api/plates')
def get_plates():
    return jsonify(controller.authorized_plates)

@app.route('/api/plates', methods=['POST'])
def add_plate():
    data = request.json
    plate = data.get('plate', '').upper()
    name = data.get('name', '')
    
    if not plate:
        return jsonify({'error': 'Placa requerida'}), 400
    
    controller.authorized_plates[plate] = {'name': name}
    controller.save_authorized_plates()
    
    return jsonify({'success': True})

@app.route('/api/plates/<plate>', methods=['DELETE'])
def delete_plate(plate):
    plate = plate.upper()
    if plate in controller.authorized_plates:
        del controller.authorized_plates[plate]
        controller.save_authorized_plates()
        return jsonify({'success': True})
    
    return jsonify({'error': 'Placa no encontrada'}), 404

@app.route('/api/stats')
def get_stats():
    logs = controller.get_recent_logs(24)
    
    stats = {
        'total_accesos': len(logs),
        'autorizados': len([l for l in logs if l.get('autorizado')]),
        'no_autorizados': len([l for l in logs if not l.get('autorizado')]),
        'entradas': len([l for l in logs if l.get('ubicacion') == 'entrada']),
        'salidas': len([l for l in logs if l.get('ubicacion') == 'salida']),
        'total_placas': len(controller.authorized_plates)
    }
    
    return jsonify(stats)

@app.route('/api/open-gate', methods=['POST'])
def manual_open_gate():
    try:
        data = request.json or {}
        duration = data.get('duration', 10)
        
        if not isinstance(duration, int) or duration < 1 or duration > 30:
            return jsonify({'error': 'Duración debe ser entre 1 y 30 segundos'}), 400
        
        controller.gate_relay.manual_open(duration)
        
        # Registrar evento manual
        event = {
            "timestamp": datetime.now().isoformat(),
            "ubicacion": "manual",
            "placa": "APERTURA_MANUAL",
            "autorizado": True,
            "imagen": None,
            "duracion": duration
        }
        
        filename = f"logs/event_manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("logs", exist_ok=True)
        with open(filename, "w") as f:
            json.dump(event, f, indent=2)
        
        return jsonify({'success': True, 'message': f'Portón abierto por {duration} segundos'})
        
    except Exception as e:
        return jsonify({'error': f'Error abriendo portón: {str(e)}'}), 500

@app.route('/images/<path:filename>')
def serve_image(filename):
    try:
        # Construir ruta completa de la imagen
        image_path = os.path.join(config.SNAPSHOTS_DIR, filename)
        
        # Verificar que el archivo existe
        if not os.path.exists(image_path):
            return jsonify({'error': 'Imagen no encontrada'}), 404
        
        # Servir la imagen
        return send_file(image_path)
    except Exception as e:
        return jsonify({'error': f'Error sirviendo imagen: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
