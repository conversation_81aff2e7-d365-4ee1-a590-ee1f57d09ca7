from flask import Flask, render_template, jsonify, request, send_file
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
import json
import os
from datetime import datetime, timedelta
from config import config
import secrets

# Importar la función para obtener el relé compartido
from gate_control_system import get_gate_relay

app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)

# CORS configuration for development
CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000"])

# Rate limiting
limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

class WebController:
    def __init__(self):
        self.authorized_plates = self.load_authorized_plates()
        # Usar instancia compartida del relé
        self.gate_relay = get_gate_relay()
    
    def load_authorized_plates(self):
        try:
            with open(config.AUTHORIZED_PLATES_FILE, "r") as f:
                return json.load(f)
        except:
            return {}
    
    def save_authorized_plates(self):
        with open(config.AUTHORIZED_PLATES_FILE, "w") as f:
            json.dump(self.authorized_plates, f, indent=2)
    
    def get_recent_logs(self, hours=24):
        logs = []
        log_dir = config.LOGS_DIR
        
        if not os.path.exists(log_dir):
            return logs
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        for filename in os.listdir(log_dir):
            if filename.startswith("event_") and filename.endswith(".json"):
                filepath = os.path.join(log_dir, filename)
                try:
                    with open(filepath, "r") as f:
                        log_data = json.load(f)
                        log_time = datetime.fromisoformat(log_data["timestamp"])
                        if log_time > cutoff_time:
                            logs.append(log_data)
                except:
                    continue
        
        return sorted(logs, key=lambda x: x["timestamp"], reverse=True)

controller = WebController()

@app.route('/')
def dashboard():
    return render_template('dashboard.html')

@app.route('/api/logs')
def get_logs():
    hours = request.args.get('hours', 24, type=int)
    logs = controller.get_recent_logs(hours)
    return jsonify(logs)

@app.route('/api/plates')
def get_plates():
    return jsonify(controller.authorized_plates)

@app.route('/api/plates', methods=['POST'])
@limiter.limit("10 per minute")
def add_plate():
    try:
        data = request.json
        if not data:
            return jsonify({'error': 'Datos JSON requeridos'}), 400

        plate = data.get('plate', '').strip().upper()
        name = data.get('name', '').strip()

        # Validate plate format (basic validation)
        if not plate or len(plate) < 3 or len(plate) > 10:
            return jsonify({'error': 'Formato de placa inválido'}), 400

        # Validate name
        if not name or len(name) > 100:
            return jsonify({'error': 'Nombre requerido (máximo 100 caracteres)'}), 400

        controller.authorized_plates[plate] = {
            'name': name,
            'added_date': datetime.now().isoformat(),
            'added_by': get_remote_address()
        }
        controller.save_authorized_plates()

        app.logger.info(f"New plate added: {plate} by {get_remote_address()}")
        return jsonify({'success': True, 'plate': plate})

    except Exception as e:
        app.logger.error(f"Error adding plate: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/plates/<plate>', methods=['DELETE'])
def delete_plate(plate):
    plate = plate.upper()
    if plate in controller.authorized_plates:
        del controller.authorized_plates[plate]
        controller.save_authorized_plates()
        return jsonify({'success': True})
    
    return jsonify({'error': 'Placa no encontrada'}), 404

@app.route('/api/stats')
def get_stats():
    logs = controller.get_recent_logs(24)
    
    stats = {
        'total_accesos': len(logs),
        'autorizados': len([l for l in logs if l.get('autorizado')]),
        'no_autorizados': len([l for l in logs if not l.get('autorizado')]),
        'entradas': len([l for l in logs if l.get('ubicacion') == 'entrada']),
        'salidas': len([l for l in logs if l.get('ubicacion') == 'salida']),
        'total_placas': len(controller.authorized_plates)
    }
    
    return jsonify(stats)

@app.route('/api/open-gate', methods=['POST'])
@limiter.limit("5 per minute")  # Limit critical operations
def manual_open_gate():
    try:
        data = request.json or {}
        duration = data.get('duration', 10)

        if not isinstance(duration, int) or duration < 1 or duration > 30:
            return jsonify({'error': 'Duración debe ser entre 1 y 30 segundos'}), 400

        # Log the manual operation attempt
        client_ip = get_remote_address()
        app.logger.warning(f"Manual gate operation requested from IP: {client_ip}, duration: {duration}s")

        controller.gate_relay.manual_open(duration)

        # Registrar evento manual
        event = {
            "timestamp": datetime.now().isoformat(),
            "ubicacion": "manual",
            "placa": "APERTURA_MANUAL",
            "autorizado": True,
            "imagen": None,
            "duracion": duration,
            "ip_origen": client_ip
        }

        filename = f"logs/event_manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("logs", exist_ok=True)
        with open(filename, "w") as f:
            json.dump(event, f, indent=2)

        return jsonify({'success': True, 'message': f'Portón abierto por {duration} segundos'})

    except Exception as e:
        app.logger.error(f"Error in manual gate operation: {str(e)}")
        return jsonify({'error': f'Error abriendo portón: {str(e)}'}), 500

@app.route('/api/health')
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check if relay is accessible
        relay_status = "ok" if controller.gate_relay else "error"

        # Check if logs directory exists
        logs_status = "ok" if os.path.exists(config.LOGS_DIR) else "error"

        # Check if authorized plates file exists
        plates_status = "ok" if os.path.exists(config.AUTHORIZED_PLATES_FILE) else "warning"

        health_data = {
            "status": "healthy" if all(s == "ok" for s in [relay_status, logs_status]) else "degraded",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "relay": relay_status,
                "logs": logs_status,
                "plates_file": plates_status
            },
            "version": "1.0.0"
        }

        status_code = 200 if health_data["status"] == "healthy" else 503
        return jsonify(health_data), status_code

    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 503

@app.route('/images/<path:filename>')
def serve_image(filename):
    try:
        # Validate filename to prevent directory traversal
        if '..' in filename or filename.startswith('/'):
            return jsonify({'error': 'Nombre de archivo inválido'}), 400

        # Construir ruta completa de la imagen
        image_path = os.path.join(config.SNAPSHOTS_DIR, filename)

        # Verificar que el archivo existe y está dentro del directorio permitido
        if not os.path.exists(image_path) or not os.path.commonpath([config.SNAPSHOTS_DIR, image_path]) == config.SNAPSHOTS_DIR:
            return jsonify({'error': 'Imagen no encontrada'}), 404

        # Servir la imagen
        return send_file(image_path)
    except Exception as e:
        app.logger.error(f"Error serving image {filename}: {str(e)}")
        return jsonify({'error': 'Error sirviendo imagen'}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint no encontrado'}), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({'error': 'Método no permitido'}), 405

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({'error': 'Demasiadas solicitudes. Intente más tarde.'}), 429

@app.errorhandler(500)
def internal_error(error):
    app.logger.error(f"Internal server error: {str(error)}")
    return jsonify({'error': 'Error interno del servidor'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=False)
