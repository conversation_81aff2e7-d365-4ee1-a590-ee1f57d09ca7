from time import sleep
import logging
from gpiozero import OutputDevice

class RelayModule:
    def __init__(self, pin=17, active_high=False, initial_value=False):
        self.pin = pin
        self.relay = OutputDevice(pin, active_high=active_high, initial_value=initial_value)
        self.logger = logging.getLogger("gate_system")
        self.logger.info(f"📡 Relé del portón inicializado")


    def pulse(self):
        '''Pulso mandado para abrir y cerrar porton'''
        try:
            self.relay.on()
            sleep(1)
            self.relay.off()
        except Exception as e:
            self.logger.error(f"Error controlando relé: {e}")
            self.relay.off()


    def cleanup(self):
        try:
            self.relay.off()
            self.relay.close()
            self.logger.info("Relé del portón cerrado correctamente")
        except Exception as e:
            self.logger.error(f"Error cerrando relé: {e}")

