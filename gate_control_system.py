import time
import json
import os
from config import config
from datetime import datetime
from utils.net import online
from utils.logger import setup_logger
from hardware.camera import Camera
from hardware.relay_module import RelayModule
from hardware.distance_sensor import DistanceSensor
from hardware.reed_switch import ReedSwitch

_gate_relay_instance = None

def get_gate_relay():
    global _gate_relay_instance
    if _gate_relay_instance is None:
        _gate_relay_instance = RelayModule(pin=config.RELAY_PIN)
    return _gate_relay_instance

class GateControlSystem:
    
    def __init__(self):
        self.logger = setup_logger("gate_system")
        self.logger.info("🚀 Iniciando sistema de control de portón...")

        self.camera_entrada = Camera(config.RTSP_CAMERA_ENTRADA, "entrada")
        self.camera_salida = Camera(config.RTSP_CAMERA_SALIDA, "salida")

        self.sensor_entrada = DistanceSensor(
            trigger_pin=config.PIN_SENSOR_ENTRADA_TRIGGER,
            echo_pin=config.PIN_SENSOR_ENTRADA_ECHO,
            threshold_distance=1.5
        )
        self.sensor_salida = DistanceSensor(
            trigger_pin=config.PIN_SENSOR_SALIDA_TRIGGER,
            echo_pin=config.PIN_SENSOR_SALIDA_ECHO,
            threshold_distance=1.5
        )
        self.sensor_pasada = DistanceSensor(
            trigger_pin=config.PIN_SENSOR_PASADA_TRIGGER,
            echo_pin=config.PIN_SENSOR_PASADA_ECHO,
            threshold_distance=2.0
        )

        self.gate_state = ReedSwitch(pin=18, active_high=False)
        
        # self.gate_state.add_state_change_callback(self._on_gate_state_change)

        self.gate_relay = get_gate_relay()
        # self.gate_is_open = False
        
        self.create_directories()
        
        self.logger.info("✅ Sistema inicializado correctamente")
    
    
    def create_directories(self):
        os.makedirs(os.path.join(config.SNAPSHOTS_DIR, "entrada"), exist_ok=True)
        os.makedirs(os.path.join(config.SNAPSHOTS_DIR, "salida"), exist_ok=True)
        os.makedirs("logs", exist_ok=True)
    
    
    def is_plate_authorized(self, plate):
        try:
            with open(config.AUTHORIZED_PLATES_FILE, "r") as f:
                authorized_plates = json.load(f)
            
            if plate in authorized_plates:
                return True, authorized_plates[plate]
            return False, None
        except Exception as e:
            self.logger.error(f"❌ Error verificando placa: {e}")
            return False, None
    
    
    def save_event_log(self, plate, authorized, info, location, image_path):
        try:
            relative_image_path = None
            if image_path:
                relative_image_path = os.path.relpath(image_path, config.SNAPSHOTS_DIR)
            
            event = {
                "timestamp": datetime.now().isoformat(),
                "ubicacion": location,
                "placa": plate,
                "autorizado": authorized,
                "detalles": info,
                "imagen": relative_image_path
            }
            
            filename = f"logs/event_{location}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w") as f:
                json.dump(event, f, indent=2)
            
            self.logger.info(f"📝 Evento guardado: {filename}")
        
        except Exception as e:
            self.logger.error(f"❌ Error guardando evento: {e}")
    
    
    # def _on_gate_state_change(self, is_closed):
    #     if is_closed:
    #         self.gate_is_open = False
    #         self.logger.info("🔒 Portón detectado como cerrado")
    #     else:
    #         self.gate_is_open = True
    #         self.logger.info("🔓 Portón detectado como abierto")
    
    
    def open_gate(self):
        self.logger.info("🔒 Abriendo portón...")
        try:
            self.gate_relay.pulse()
        except Exception as e:
            self.logger.error(f"❌ Error abriendo portón: {e}")
    
    
    def close_gate(self):        
        self.logger.info("🔒 Cerrando portón...")
        try:
            time.sleep(3)
            self.gate_relay.pulse()
        except Exception as e:
            self.logger.error(f"❌ Error cerrando portón: {e}")

    
    def is_authorized_vehicle(self, location):
        image_path = self.camera_entrada.take_snapshot("entrada") if location == "entrada" else self.camera_salida.take_snapshot("salida")
        if not image_path:
            self.logger.warning(f"⚠️ No se pudo capturar imagen en {location}")
            time.sleep(config.CYCLE_INTERVAL)
            return False

        if not online():
            self.logger.warning("⚠️ Sin internet")
            time.sleep(config.CYCLE_INTERVAL)
            return False
        
        plate = self.camera_entrada.recognize_plate(image_path) if location == "entrada" else self.camera_salida.recognize_plate(image_path)
        if not plate:
            self.logger.warning(f"⚠️ No se pudo reconocer la placa en {location}")
            time.sleep(config.CYCLE_INTERVAL)
            return False

        authorized, info = self.is_plate_authorized(plate)
        if not authorized:
            self.logger.warning(f"⚠️ Placa [{plate}] no autorizada en {location}")
            self.save_event_log(plate, authorized, info, location, image_path)
            return False
        
        self.logger.info(f"✅ Placa [{plate}] autorizada en {location}")
        self.save_event_log(plate, authorized, info, location, image_path)
        return authorized

    
    def run(self):
        try:
            while True:
                if self.gate_state.is_open():
                    if (self.sensor_entrada.object_detected()
                        or self.sensor_salida.object_detected()
                        or self.sensor_pasada.object_detected()):
                        continue
                    else:
                        self.close_gate()
                        time.sleep(15)

                if self.gate_state.is_closed():
                    if self.sensor_salida.object_detected():
                        if self.is_authorized_vehicle("salida"):
                            self.open_gate()
                            time.sleep(15)
                        else: 
                            self.logger.warning("⚠️ Vehículo no autorizado intentando salir")
                            continue

                    elif self.sensor_entrada.object_detected():
                        if self.is_authorized_vehicle("entrada"):
                            self.open_gate()
                            time.sleep(15)
                        else: 
                            continue

                time.sleep(3)
                
        except KeyboardInterrupt:
            self.logger.info("🛑 Sistema detenido por usuario")
        finally:
            self.cleanup()
    
    
    def cleanup(self):
        if hasattr(self, 'sensor_entrada'):
            self.sensor_entrada.cleanup()
        if hasattr(self, 'sensor_salida'):
            self.sensor_salida.cleanup()
        if hasattr(self, 'sensor_pasada'):
            self.sensor_pasada.cleanup()
        if hasattr(self, 'camera_entrada'):
            self.camera_entrada.cleanup()
        if hasattr(self, 'camera_salida'):
            self.camera_salida.cleanup()
        if hasattr(self, 'gate_state'):
            self.gate_state.cleanup()
        if hasattr(self, 'gate_relay'):
            self.gate_relay.cleanup()
        self.logger.info("🧹 Recursos liberados")

if __name__ == "__main__":
    system = GateControlSystem()
    system.run()
